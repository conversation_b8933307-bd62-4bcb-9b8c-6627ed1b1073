"""
Quality Scoring and Validation System
Comprehensive quality scoring with automated validation, monitoring, and reporting
"""

import logging
import json
import time
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
import re
from datetime import datetime

class QualityDimension(Enum):
    """Quality dimensions for scoring"""
    COMPLETENESS = "completeness"
    ACCURACY = "accuracy"
    CONSISTENCY = "consistency"
    RELEVANCE = "relevance"
    FRESHNESS = "freshness"

class QualityLevel(Enum):
    """Quality levels"""
    EXCELLENT = "excellent"  # 90-100%
    GOOD = "good"           # 75-89%
    FAIR = "fair"           # 60-74%
    POOR = "poor"           # 40-59%
    CRITICAL = "critical"   # 0-39%

@dataclass
class QualityScore:
    """Individual quality score for a dimension"""
    dimension: QualityDimension
    score: float  # 0.0 to 1.0
    weight: float  # Importance weight
    details: Dict[str, Any]
    timestamp: float

@dataclass
class QualityReport:
    """Comprehensive quality report"""
    entity_id: str
    entity_type: str
    overall_score: float
    quality_level: QualityLevel
    dimension_scores: Dict[QualityDimension, QualityScore]
    validation_errors: List[str]
    recommendations: List[str]
    timestamp: float

class QualityScoringSystem:
    """
    Comprehensive quality scoring system with automated validation
    Provides monitoring and reporting for data quality
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Quality dimension weights
        self.dimension_weights = {
            QualityDimension.COMPLETENESS: 0.3,  # Most important
            QualityDimension.ACCURACY: 0.25,
            QualityDimension.CONSISTENCY: 0.2,
            QualityDimension.RELEVANCE: 0.15,
            QualityDimension.FRESHNESS: 0.1
        }
        
        # Quality thresholds (more strict)
        self.quality_thresholds = {
            QualityLevel.EXCELLENT: 0.95,
            QualityLevel.GOOD: 0.8,
            QualityLevel.FAIR: 0.65,
            QualityLevel.POOR: 0.45,
            QualityLevel.CRITICAL: 0.0
        }
        
        # Required fields by entity type
        self.required_fields = {
            'ai-tool': [
                'name', 'website_url', 'short_description', 'description',
                'key_features', 'pricing_model', 'technical_level'
            ],
            'research-paper': [
                'name', 'website_url', 'short_description', 'description',
                'authors', 'publication_date', 'venue'
            ],
            'hardware': [
                'name', 'website_url', 'short_description', 'description',
                'specifications', 'price_range'
            ]
        }
        
        # Quality history for monitoring
        self.quality_history = []
        self.quality_trends = {}
    
    def calculate_quality_score(self, data: Dict[str, Any], entity_type: str = 'ai-tool') -> QualityReport:
        """
        Calculate comprehensive quality score for data
        
        Args:
            data: Entity data to score
            entity_type: Type of entity being scored
            
        Returns:
            QualityReport with detailed scoring
        """
        
        self.logger.info(f"Calculating quality score for {entity_type}")
        
        entity_id = data.get('name', 'unknown')
        dimension_scores = {}
        validation_errors = []
        recommendations = []
        
        # Calculate scores for each dimension
        for dimension in QualityDimension:
            score_result = self._calculate_dimension_score(dimension, data, entity_type)
            dimension_scores[dimension] = score_result
            
            # Collect recommendations for low scores
            if score_result.score < 0.7:
                recommendations.extend(self._get_dimension_recommendations(dimension, score_result))
        
        # Calculate overall weighted score
        overall_score = sum(
            score.score * self.dimension_weights[dimension]
            for dimension, score in dimension_scores.items()
        )
        
        # Determine quality level
        quality_level = self._determine_quality_level(overall_score)
        
        # Generate validation errors for critical issues
        if quality_level in [QualityLevel.POOR, QualityLevel.CRITICAL]:
            validation_errors = self._generate_validation_errors(dimension_scores)
        
        # Create quality report
        report = QualityReport(
            entity_id=entity_id,
            entity_type=entity_type,
            overall_score=overall_score,
            quality_level=quality_level,
            dimension_scores=dimension_scores,
            validation_errors=validation_errors,
            recommendations=recommendations,
            timestamp=time.time()
        )
        
        # Store for monitoring
        self.quality_history.append(report)
        self._update_quality_trends(report)
        
        self.logger.info(f"Quality score calculated: {overall_score:.2f} ({quality_level.value})")
        return report
    
    def _calculate_dimension_score(self, dimension: QualityDimension, data: Dict[str, Any], 
                                 entity_type: str) -> QualityScore:
        """Calculate score for a specific quality dimension"""
        
        if dimension == QualityDimension.COMPLETENESS:
            return self._score_completeness(data, entity_type)
        elif dimension == QualityDimension.ACCURACY:
            return self._score_accuracy(data, entity_type)
        elif dimension == QualityDimension.CONSISTENCY:
            return self._score_consistency(data, entity_type)
        elif dimension == QualityDimension.RELEVANCE:
            return self._score_relevance(data, entity_type)
        elif dimension == QualityDimension.FRESHNESS:
            return self._score_freshness(data, entity_type)
        else:
            return QualityScore(dimension, 0.0, 0.0, {}, time.time())
    
    def _score_completeness(self, data: Dict[str, Any], entity_type: str) -> QualityScore:
        """Score data completeness"""
        
        required_fields = self.required_fields.get(entity_type, self.required_fields['ai-tool'])
        
        # Count present and non-empty fields
        present_fields = 0
        total_fields = len(required_fields)
        missing_fields = []
        
        for field in required_fields:
            if field in data and data[field] and str(data[field]).strip():
                present_fields += 1
            else:
                missing_fields.append(field)
        
        # Calculate completeness score
        completeness_score = present_fields / total_fields if total_fields > 0 else 0.0
        
        # Bonus for optional fields
        optional_fields = ['logo_url', 'documentation_url', 'contact_url', 'founded_year']
        bonus_fields = sum(1 for field in optional_fields if field in data and data[field])
        bonus_score = min(bonus_fields * 0.05, 0.2)  # Max 20% bonus
        
        final_score = min(completeness_score + bonus_score, 1.0)
        
        details = {
            'present_fields': present_fields,
            'total_required': total_fields,
            'missing_fields': missing_fields,
            'bonus_fields': bonus_fields,
            'base_score': completeness_score,
            'bonus_score': bonus_score
        }
        
        return QualityScore(
            QualityDimension.COMPLETENESS,
            final_score,
            self.dimension_weights[QualityDimension.COMPLETENESS],
            details,
            time.time()
        )
    
    def _score_accuracy(self, data: Dict[str, Any], entity_type: str) -> QualityScore:
        """Score data accuracy"""
        
        accuracy_score = 1.0
        accuracy_issues = []
        
        # Check URL validity
        url_fields = ['website_url', 'logo_url', 'documentation_url', 'contact_url']
        for field in url_fields:
            if field in data and data[field]:
                if not self._is_valid_url(data[field]):
                    accuracy_score -= 0.1
                    accuracy_issues.append(f"Invalid URL in {field}")
        
        # Check enum values
        enum_fields = {
            'pricing_model': ['FREE', 'FREEMIUM', 'SUBSCRIPTION', 'PAY_PER_USE', 'ONE_TIME_PURCHASE', 'CONTACT_SALES', 'OPEN_SOURCE'],
            'price_range': ['FREE', 'LOW', 'MEDIUM', 'HIGH', 'ENTERPRISE'],
            'technical_level': ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']
        }
        
        for field, valid_values in enum_fields.items():
            if field in data and data[field]:
                if data[field] not in valid_values:
                    accuracy_score -= 0.15
                    accuracy_issues.append(f"Invalid enum value in {field}: {data[field]}")
        
        # Check data types
        if 'founded_year' in data and data['founded_year']:
            try:
                year = int(data['founded_year'])
                if year < 1900 or year > datetime.now().year:
                    accuracy_score -= 0.1
                    accuracy_issues.append(f"Invalid founded year: {year}")
            except (ValueError, TypeError):
                accuracy_score -= 0.1
                accuracy_issues.append("Founded year is not a valid integer")
        
        # Check description quality
        if 'description' in data and data['description']:
            desc_length = len(data['description'])
            if desc_length < 50:
                accuracy_score -= 0.1
                accuracy_issues.append("Description too short")
            elif desc_length > 2000:
                accuracy_score -= 0.05
                accuracy_issues.append("Description too long")
        
        final_score = max(accuracy_score, 0.0)
        
        details = {
            'accuracy_issues': accuracy_issues,
            'deductions': 1.0 - final_score
        }
        
        return QualityScore(
            QualityDimension.ACCURACY,
            final_score,
            self.dimension_weights[QualityDimension.ACCURACY],
            details,
            time.time()
        )
    
    def _score_consistency(self, data: Dict[str, Any], entity_type: str) -> QualityScore:
        """Score data consistency"""
        
        consistency_score = 1.0
        consistency_issues = []
        
        # Check pricing consistency
        pricing_model = data.get('pricing_model', '')
        price_range = data.get('price_range', '')
        has_free_tier = data.get('has_free_tier', False)
        
        if pricing_model == 'FREE' and price_range != 'FREE':
            consistency_score -= 0.2
            consistency_issues.append("Free pricing model inconsistent with price range")
        
        if pricing_model == 'FREE' and not has_free_tier:
            consistency_score -= 0.1
            consistency_issues.append("Free pricing model but has_free_tier is False")
        
        if pricing_model in ['FREEMIUM', 'SUBSCRIPTION'] and not has_free_tier and price_range == 'FREE':
            consistency_score -= 0.1
            consistency_issues.append("Pricing model suggests paid tiers but price range is FREE")
        
        # Check technical level vs features consistency
        technical_level = data.get('technical_level', '')
        key_features = data.get('key_features', [])
        
        if technical_level == 'BEGINNER':
            advanced_keywords = ['api', 'programming', 'code', 'development', 'advanced']
            if any(keyword in ' '.join(key_features).lower() for keyword in advanced_keywords):
                consistency_score -= 0.1
                consistency_issues.append("Beginner level but features suggest advanced usage")
        
        # Check target audience vs technical level
        target_audience = data.get('target_audience', [])
        if technical_level == 'EXPERT' and any('beginner' in aud.lower() for aud in target_audience):
            consistency_score -= 0.1
            consistency_issues.append("Expert level but targets beginners")
        
        final_score = max(consistency_score, 0.0)
        
        details = {
            'consistency_issues': consistency_issues,
            'deductions': 1.0 - final_score
        }
        
        return QualityScore(
            QualityDimension.CONSISTENCY,
            final_score,
            self.dimension_weights[QualityDimension.CONSISTENCY],
            details,
            time.time()
        )
    
    def _score_relevance(self, data: Dict[str, Any], entity_type: str) -> QualityScore:
        """Score data relevance"""
        
        relevance_score = 0.8  # Base score
        relevance_factors = []
        
        # Check if features are relevant to description
        description = data.get('description', '').lower()
        key_features = data.get('key_features', [])
        
        if description and key_features:
            feature_relevance = 0
            for feature in key_features:
                if any(word in description for word in feature.lower().split()):
                    feature_relevance += 1
            
            if len(key_features) > 0:
                feature_match_ratio = feature_relevance / len(key_features)
                relevance_score += feature_match_ratio * 0.2
                relevance_factors.append(f"Feature relevance: {feature_match_ratio:.2f}")
        
        # Check use cases relevance
        use_cases = data.get('use_cases', [])
        if description and use_cases:
            use_case_relevance = sum(1 for use_case in use_cases 
                                   if any(word in description for word in use_case.lower().split()))
            if len(use_cases) > 0:
                use_case_ratio = use_case_relevance / len(use_cases)
                relevance_score += use_case_ratio * 0.1
                relevance_factors.append(f"Use case relevance: {use_case_ratio:.2f}")
        
        final_score = min(relevance_score, 1.0)
        
        details = {
            'relevance_factors': relevance_factors,
            'base_score': 0.8,
            'bonus_score': final_score - 0.8
        }
        
        return QualityScore(
            QualityDimension.RELEVANCE,
            final_score,
            self.dimension_weights[QualityDimension.RELEVANCE],
            details,
            time.time()
        )
    
    def _score_freshness(self, data: Dict[str, Any], entity_type: str) -> QualityScore:
        """Score data freshness"""
        
        # For now, assume all data is fresh since we don't have timestamps
        # In a real implementation, this would check last_updated fields
        freshness_score = 0.9  # Assume recent data
        
        freshness_factors = []
        
        # Check if founded year is recent (bonus for newer companies)
        if 'founded_year' in data and data['founded_year']:
            try:
                founded_year = int(data['founded_year'])
                current_year = datetime.now().year
                years_old = current_year - founded_year
                
                if years_old <= 2:
                    freshness_score = min(freshness_score + 0.1, 1.0)
                    freshness_factors.append("Recently founded company")
                elif years_old > 10:
                    freshness_score = max(freshness_score - 0.1, 0.5)
                    freshness_factors.append("Established company")
            except (ValueError, TypeError):
                pass
        
        details = {
            'freshness_factors': freshness_factors,
            'base_score': 0.9
        }
        
        return QualityScore(
            QualityDimension.FRESHNESS,
            freshness_score,
            self.dimension_weights[QualityDimension.FRESHNESS],
            details,
            time.time()
        )
    
    def _is_valid_url(self, url: str) -> bool:
        """Check if URL is valid"""
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return url_pattern.match(url) is not None
    
    def _determine_quality_level(self, score: float) -> QualityLevel:
        """Determine quality level from score"""
        
        if score >= self.quality_thresholds[QualityLevel.EXCELLENT]:
            return QualityLevel.EXCELLENT
        elif score >= self.quality_thresholds[QualityLevel.GOOD]:
            return QualityLevel.GOOD
        elif score >= self.quality_thresholds[QualityLevel.FAIR]:
            return QualityLevel.FAIR
        elif score >= self.quality_thresholds[QualityLevel.POOR]:
            return QualityLevel.POOR
        else:
            return QualityLevel.CRITICAL
    
    def _generate_validation_errors(self, dimension_scores: Dict[QualityDimension, QualityScore]) -> List[str]:
        """Generate validation errors for low quality scores"""
        
        errors = []
        
        for dimension, score in dimension_scores.items():
            if score.score < 0.5:  # Critical threshold
                errors.append(f"Critical {dimension.value} issue: score {score.score:.2f}")
                
                # Add specific details
                if 'missing_fields' in score.details:
                    missing = score.details['missing_fields']
                    if missing:
                        errors.append(f"Missing required fields: {', '.join(missing)}")
                
                if 'accuracy_issues' in score.details:
                    errors.extend(score.details['accuracy_issues'])
                
                if 'consistency_issues' in score.details:
                    errors.extend(score.details['consistency_issues'])
        
        return errors
    
    def _get_dimension_recommendations(self, dimension: QualityDimension, score: QualityScore) -> List[str]:
        """Get recommendations for improving dimension score"""
        
        recommendations = []
        
        if dimension == QualityDimension.COMPLETENESS:
            if 'missing_fields' in score.details:
                missing = score.details['missing_fields']
                if missing:
                    recommendations.append(f"Add missing required fields: {', '.join(missing)}")
        
        elif dimension == QualityDimension.ACCURACY:
            if 'accuracy_issues' in score.details:
                recommendations.append("Fix accuracy issues: validate URLs, enum values, and data types")
        
        elif dimension == QualityDimension.CONSISTENCY:
            if 'consistency_issues' in score.details:
                recommendations.append("Resolve consistency issues between related fields")
        
        elif dimension == QualityDimension.RELEVANCE:
            recommendations.append("Improve relevance by aligning features and use cases with description")
        
        elif dimension == QualityDimension.FRESHNESS:
            recommendations.append("Update data to ensure freshness and accuracy")
        
        return recommendations
    
    def _update_quality_trends(self, report: QualityReport):
        """Update quality trends for monitoring"""
        
        entity_type = report.entity_type
        if entity_type not in self.quality_trends:
            self.quality_trends[entity_type] = {
                'scores': [],
                'avg_score': 0.0,
                'trend': 'stable'
            }
        
        trends = self.quality_trends[entity_type]
        trends['scores'].append(report.overall_score)
        
        # Keep only last 100 scores
        if len(trends['scores']) > 100:
            trends['scores'] = trends['scores'][-100:]
        
        # Update average
        trends['avg_score'] = sum(trends['scores']) / len(trends['scores'])
        
        # Determine trend
        if len(trends['scores']) >= 10:
            recent_avg = sum(trends['scores'][-10:]) / 10
            older_avg = sum(trends['scores'][-20:-10]) / 10 if len(trends['scores']) >= 20 else recent_avg
            
            if recent_avg > older_avg + 0.05:
                trends['trend'] = 'improving'
            elif recent_avg < older_avg - 0.05:
                trends['trend'] = 'declining'
            else:
                trends['trend'] = 'stable'
    
    def get_quality_monitoring_report(self) -> Dict[str, Any]:
        """Get comprehensive quality monitoring report"""
        
        if not self.quality_history:
            return {'message': 'No quality data available'}
        
        # Overall statistics
        total_reports = len(self.quality_history)
        avg_score = sum(r.overall_score for r in self.quality_history) / total_reports
        
        # Quality level distribution
        level_counts = {}
        for level in QualityLevel:
            level_counts[level.value] = sum(1 for r in self.quality_history if r.quality_level == level)
        
        # Recent performance (last 24 hours or last 50 reports)
        recent_reports = self.quality_history[-50:]
        recent_avg = sum(r.overall_score for r in recent_reports) / len(recent_reports)
        
        # Dimension performance
        dimension_performance = {}
        for dimension in QualityDimension:
            scores = []
            for report in self.quality_history:
                if dimension in report.dimension_scores:
                    scores.append(report.dimension_scores[dimension].score)
            
            if scores:
                dimension_performance[dimension.value] = {
                    'avg_score': sum(scores) / len(scores),
                    'min_score': min(scores),
                    'max_score': max(scores)
                }
        
        return {
            'total_reports': total_reports,
            'overall_avg_score': avg_score,
            'recent_avg_score': recent_avg,
            'quality_level_distribution': level_counts,
            'dimension_performance': dimension_performance,
            'quality_trends': self.quality_trends,
            'timestamp': time.time()
        }
