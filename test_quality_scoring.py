"""
Test script for Quality Scoring System
Tests comprehensive quality scoring with validation and monitoring
"""

import sys
sys.path.append('/app')

from quality_scoring_system import QualityScoringSystem, QualityLevel, QualityDimension
import time

def test_quality_scoring():
    """Test quality scoring with different data quality levels"""
    
    print("📊 TESTING QUALITY SCORING SYSTEM")
    print("=" * 50)
    
    scorer = QualityScoringSystem()
    
    # Test cases with different quality levels
    test_cases = [
        {
            "name": "High Quality Tool",
            "data": {
                "name": "Excellent AI Tool",
                "website_url": "https://example.com",
                "short_description": "A comprehensive AI tool for productivity",
                "description": "This is a comprehensive AI-powered productivity tool that helps users streamline their workflows, automate repetitive tasks, and enhance their overall efficiency. It features advanced machine learning algorithms, intuitive user interface, and seamless integrations with popular platforms.",
                "key_features": ["AI automation", "workflow optimization", "seamless integrations", "intuitive interface", "advanced analytics"],
                "use_cases": ["business automation", "personal productivity", "team collaboration"],
                "pricing_model": "FREEMIUM",
                "price_range": "MEDIUM",
                "technical_level": "INTERMEDIATE",
                "target_audience": ["professionals", "businesses", "teams"],
                "has_free_tier": True,
                "has_api": True,
                "logo_url": "https://example.com/logo.png",
                "documentation_url": "https://example.com/docs",
                "contact_url": "https://example.com/contact",
                "founded_year": 2020
            },
            "expected_level": QualityLevel.EXCELLENT
        },
        {
            "name": "Medium Quality Tool",
            "data": {
                "name": "Good AI Tool",
                "website_url": "https://example.com",
                "short_description": "AI tool for productivity",
                "description": "This AI tool helps with productivity tasks and workflow automation.",
                "key_features": ["automation", "productivity"],
                "pricing_model": "SUBSCRIPTION",
                "price_range": "MEDIUM",
                "technical_level": "BEGINNER"
            },
            "expected_level": QualityLevel.GOOD
        },
        {
            "name": "Low Quality Tool",
            "data": {
                "name": "Basic Tool",
                "website_url": "invalid-url",
                "short_description": "Tool",
                "description": "Short desc",
                "pricing_model": "INVALID_MODEL",
                "technical_level": "INVALID_LEVEL"
            },
            "expected_level": QualityLevel.POOR
        },
        {
            "name": "Incomplete Tool",
            "data": {
                "name": "Incomplete Tool"
                # Missing most required fields
            },
            "expected_level": QualityLevel.CRITICAL
        }
    ]
    
    scoring_results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            # Calculate quality score
            report = scorer.calculate_quality_score(test_case['data'], 'ai-tool')
            
            print(f"   Overall Score: {report.overall_score:.2f}")
            print(f"   Quality Level: {report.quality_level.value}")
            print(f"   Validation Errors: {len(report.validation_errors)}")
            print(f"   Recommendations: {len(report.recommendations)}")
            
            # Show dimension scores
            print(f"   Dimension Scores:")
            for dimension, score in report.dimension_scores.items():
                print(f"     {dimension.value}: {score.score:.2f}")
            
            # Check if quality level matches expectation
            level_match = report.quality_level == test_case['expected_level']
            
            if level_match:
                print(f"   ✅ Quality level matches expectation")
            else:
                print(f"   ⚠️  Expected {test_case['expected_level'].value}, got {report.quality_level.value}")
            
            scoring_results.append({
                'name': test_case['name'],
                'score': report.overall_score,
                'level': report.quality_level,
                'level_match': level_match,
                'error_count': len(report.validation_errors),
                'recommendation_count': len(report.recommendations)
            })
            
        except Exception as e:
            print(f"   💥 Error: {str(e)}")
            scoring_results.append({
                'name': test_case['name'],
                'score': 0.0,
                'level': QualityLevel.CRITICAL,
                'level_match': False,
                'error_count': 0,
                'recommendation_count': 0
            })
    
    # Analyze results
    correct_levels = sum(1 for r in scoring_results if r['level_match'])
    accuracy = (correct_levels / len(scoring_results) * 100)
    
    print(f"\n📊 QUALITY SCORING RESULTS:")
    print(f"   Correct quality levels: {correct_levels}/{len(scoring_results)}")
    print(f"   Level accuracy: {accuracy:.1f}%")
    
    # Check score distribution
    scores = [r['score'] for r in scoring_results]
    print(f"   Score range: {min(scores):.2f} - {max(scores):.2f}")
    print(f"   Average score: {sum(scores)/len(scores):.2f}")
    
    return accuracy >= 75

def test_dimension_scoring():
    """Test individual dimension scoring"""
    
    print("\n📏 TESTING DIMENSION SCORING")
    print("=" * 50)
    
    scorer = QualityScoringSystem()
    
    # Test data with specific dimension issues
    test_data = {
        "name": "Test Tool",
        "website_url": "https://example.com",
        "short_description": "Test tool for dimension scoring",
        "description": "This is a comprehensive test tool designed to evaluate the dimension scoring functionality of our quality system.",
        "key_features": ["testing", "evaluation", "scoring"],
        "use_cases": ["quality testing", "system evaluation"],
        "pricing_model": "FREEMIUM",
        "price_range": "MEDIUM",
        "technical_level": "INTERMEDIATE",
        "has_free_tier": True,
        "founded_year": 2022
    }
    
    # Test each dimension
    dimension_results = {}
    
    for dimension in QualityDimension:
        score = scorer._calculate_dimension_score(dimension, test_data, 'ai-tool')
        dimension_results[dimension] = score
        
        print(f"   {dimension.value}:")
        print(f"     Score: {score.score:.2f}")
        print(f"     Weight: {score.weight:.2f}")
        print(f"     Details: {len(score.details)} items")
        
        # Show key details
        if 'missing_fields' in score.details and score.details['missing_fields']:
            print(f"     Missing fields: {score.details['missing_fields']}")
        if 'accuracy_issues' in score.details and score.details['accuracy_issues']:
            print(f"     Accuracy issues: {len(score.details['accuracy_issues'])}")
    
    # Validate dimension scores
    all_scores = [score.score for score in dimension_results.values()]
    valid_scores = all(0.0 <= score <= 1.0 for score in all_scores)
    
    print(f"\n   All scores in valid range [0,1]: {'✅' if valid_scores else '❌'}")
    print(f"   Average dimension score: {sum(all_scores)/len(all_scores):.2f}")
    
    return valid_scores and len(dimension_results) == len(QualityDimension)

def test_quality_monitoring():
    """Test quality monitoring and reporting"""
    
    print("\n📈 TESTING QUALITY MONITORING")
    print("=" * 50)
    
    scorer = QualityScoringSystem()
    
    # Generate multiple quality reports for monitoring
    test_tools = [
        {"name": "Tool A", "website_url": "https://a.com", "short_description": "Tool A", "description": "Description A" * 10, "pricing_model": "FREE", "technical_level": "BEGINNER"},
        {"name": "Tool B", "website_url": "https://b.com", "short_description": "Tool B", "description": "Description B" * 15, "pricing_model": "FREEMIUM", "technical_level": "INTERMEDIATE"},
        {"name": "Tool C", "website_url": "https://c.com", "short_description": "Tool C", "description": "Description C" * 20, "pricing_model": "SUBSCRIPTION", "technical_level": "ADVANCED"},
        {"name": "Tool D", "website_url": "invalid", "short_description": "D", "description": "Short", "pricing_model": "INVALID", "technical_level": "INVALID"},
        {"name": "Tool E", "website_url": "https://e.com", "short_description": "Tool E", "description": "Description E" * 25, "pricing_model": "OPEN_SOURCE", "technical_level": "EXPERT"}
    ]
    
    # Score all tools
    for tool in test_tools:
        scorer.calculate_quality_score(tool, 'ai-tool')
    
    # Get monitoring report
    monitoring_report = scorer.get_quality_monitoring_report()
    
    print(f"   Monitoring Report:")
    print(f"     Total reports: {monitoring_report['total_reports']}")
    print(f"     Overall average score: {monitoring_report['overall_avg_score']:.2f}")
    print(f"     Recent average score: {monitoring_report['recent_avg_score']:.2f}")
    
    # Quality level distribution
    print(f"     Quality level distribution:")
    for level, count in monitoring_report['quality_level_distribution'].items():
        print(f"       {level}: {count}")
    
    # Dimension performance
    print(f"     Dimension performance:")
    for dimension, perf in monitoring_report['dimension_performance'].items():
        print(f"       {dimension}: avg={perf['avg_score']:.2f}, range=[{perf['min_score']:.2f}, {perf['max_score']:.2f}]")
    
    # Validate monitoring
    expected_reports = len(test_tools)
    monitoring_working = (
        monitoring_report['total_reports'] == expected_reports and
        0.0 <= monitoring_report['overall_avg_score'] <= 1.0 and
        len(monitoring_report['quality_level_distribution']) > 0
    )
    
    if monitoring_working:
        print(f"   ✅ Quality monitoring working correctly")
        return True
    else:
        print(f"   ❌ Quality monitoring issues detected")
        return False

def test_validation_and_recommendations():
    """Test validation errors and recommendations"""
    
    print("\n🔍 TESTING VALIDATION AND RECOMMENDATIONS")
    print("=" * 50)
    
    scorer = QualityScoringSystem()
    
    # Test data with multiple issues
    problematic_data = {
        "name": "Problematic Tool",
        "website_url": "not-a-url",
        "short_description": "Bad",
        "description": "Too short",
        "pricing_model": "INVALID_PRICING",
        "price_range": "INVALID_RANGE",
        "technical_level": "INVALID_LEVEL",
        "has_free_tier": False,
        "founded_year": 2050  # Future year
    }
    
    report = scorer.calculate_quality_score(problematic_data, 'ai-tool')
    
    print(f"   Tool: {problematic_data['name']}")
    print(f"   Overall Score: {report.overall_score:.2f}")
    print(f"   Quality Level: {report.quality_level.value}")
    
    print(f"\n   Validation Errors ({len(report.validation_errors)}):")
    for i, error in enumerate(report.validation_errors[:5], 1):  # Show first 5
        print(f"     {i}. {error}")
    
    print(f"\n   Recommendations ({len(report.recommendations)}):")
    for i, rec in enumerate(report.recommendations[:5], 1):  # Show first 5
        print(f"     {i}. {rec}")
    
    # Validate that errors and recommendations are generated
    has_errors = len(report.validation_errors) > 0
    has_recommendations = len(report.recommendations) > 0
    low_quality = report.quality_level in [QualityLevel.POOR, QualityLevel.CRITICAL]
    
    validation_working = has_errors and has_recommendations and low_quality
    
    if validation_working:
        print(f"\n   ✅ Validation and recommendations working")
        return True
    else:
        print(f"\n   ❌ Validation and recommendations not working properly")
        return False

if __name__ == "__main__":
    print("📊 QUALITY SCORING AND VALIDATION TESTING")
    print("=" * 60)
    
    # Run all tests
    scoring_success = test_quality_scoring()
    dimension_success = test_dimension_scoring()
    monitoring_success = test_quality_monitoring()
    validation_success = test_validation_and_recommendations()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Quality scoring: {'✅ PASS' if scoring_success else '❌ FAIL'}")
    print(f"   Dimension scoring: {'✅ PASS' if dimension_success else '❌ FAIL'}")
    print(f"   Quality monitoring: {'✅ PASS' if monitoring_success else '❌ FAIL'}")
    print(f"   Validation & recommendations: {'✅ PASS' if validation_success else '❌ FAIL'}")
    
    total_passed = sum([scoring_success, dimension_success, monitoring_success, validation_success])
    
    if total_passed >= 3:
        print("\n   🎉 QUALITY SCORING SYSTEM READY!")
        print("   📊 Comprehensive quality scoring implemented")
        print("   📈 Automated validation and monitoring functional")
        print("   🔍 Quality recommendations and reporting working")
    else:
        print("\n   ⚠️  Quality scoring system needs improvement")
